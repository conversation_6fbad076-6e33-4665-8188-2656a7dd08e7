<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Middleware;

use Bgs\LandingPages\Domain\Model\VirtualRouteContext;
use Bgs\LandingPages\Service\VirtualRouteService;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Routing\SiteRouteResult;
use TYPO3\CMS\Core\Site\Entity\Site;

/**
 * Virtual Route Handler Middleware
 *
 * This middleware runs BEFORE PageResolver to intercept virtual routes
 * and modify the request to point to the template page instead.
 * This prevents PageResolver from rejecting virtual routes with 404
 * and simplifies processing by making the request point directly to the template page.
 */
class VirtualRouteHandler implements MiddlewareInterface
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        error_log("=== VirtualRouteHandler::process() START ===");

        $site = $request->getAttribute('site');
        error_log("VirtualRouteHandler: Site = " . ($site ? $site->getIdentifier() : 'NULL'));

        if (!$site instanceof Site) {
            error_log("VirtualRouteHandler: No site found, passing through");
            return $handler->handle($request);
        }

        $path = trim($request->getUri()->getPath(), '/');
        error_log("VirtualRouteHandler: Processing path: '" . $path . "'");

        // Clear any previous virtual route state
        error_log("VirtualRouteHandler: Clearing virtual route state");
        $this->virtualRouteService->clearVirtualRoute();

        // Detect virtual route
        error_log("VirtualRouteHandler: Detecting virtual route");
        $virtualRouteMatch = $this->virtualRouteService->detectVirtualRoute($path, $site);
        error_log("VirtualRouteHandler: Virtual route match result: " . ($virtualRouteMatch ? 'YES' : 'NO'));

        if ($virtualRouteMatch) {
            error_log("VirtualRouteHandler: Processing virtual route match");
            // Load template page data to get its slug
            $templatePageUid = $virtualRouteMatch['templatePageUid'];
            error_log("VirtualRouteHandler: Template page UID: " . $templatePageUid);
            $templatePage = $this->virtualRouteService->loadTemplatePage($templatePageUid);

            if (!$templatePage) {
                error_log("VirtualRouteHandler: Template page not found, passing through");
                return $handler->handle($request);
            }
            error_log("VirtualRouteHandler: Template page loaded: " . json_encode($templatePage));

            // Use template page path instead of landing page path
            $templatePagePath = $this->getTemplatePagePath($templatePage, $site);
            error_log("VirtualRouteHandler: Template page path: " . $templatePagePath);

            // Create VirtualRouteContext entity with all necessary data
            $virtualRouteContext = VirtualRouteContext::createVirtual(
                $virtualRouteMatch['landingPage'],
                $templatePage,
                $virtualRouteMatch['flightRoute'],
                $virtualRouteMatch['originalPath'],
                $virtualRouteMatch['routeSlug'],
                $templatePageUid,
                $templatePagePath
            );

            // Store virtual route context for later processing by PSR-14 events
            $this->virtualRouteService->setVirtualRoute($virtualRouteMatch);

            // Create a new URI pointing to the template page (REQUIRED for virtual routes to work)
            // Add a unique parameter to ensure each virtual route gets its own cache entry
            $routeSlug = $virtualRouteMatch['flightRoute']['route_slug'] ?? '';
            $cacheParam = 'route=' . urlencode($routeSlug);
            $existingQuery = $request->getUri()->getQuery();
            $newQuery = $existingQuery ? $existingQuery . '&' . $cacheParam : $cacheParam;

            $newUri = $request->getUri()
                ->withPath($templatePagePath)
                ->withQuery($newQuery);

            $modifiedRequest = $request->withUri($newUri);
            // Also update the routing attribute to match the new path
            $previousResult = $request->getAttribute('routing');
            if ($previousResult instanceof SiteRouteResult) {
                // Create a new SiteRouteResult with the modified tail
                $basePath = rtrim($site->getBase()->getPath(), '/');

                // Only remove base path from the beginning of the template page path
                $newTail = $templatePagePath;
                if (strpos($templatePagePath, $basePath) === 0) {
                    $newTail = substr($templatePagePath, strlen($basePath));
                }
                $newTail = ltrim($newTail, '/');
                $newRouteResult = new SiteRouteResult(
                    $newUri,
                    $previousResult->getSite(),
                    $previousResult->getLanguage(),
                    $newTail
                );
                $modifiedRequest = $modifiedRequest->withAttribute('routing', $newRouteResult);
            }

            // Set single organized attribute instead of multiple scattered ones
            $modifiedRequest = $modifiedRequest->withAttribute('landing-pages.virtual_route_context', $virtualRouteContext);
            error_log("VirtualRouteHandler: Virtual route context set, passing modified request to next middleware");
            $response = $handler->handle($modifiedRequest);
            error_log("VirtualRouteHandler: Response received from next middleware, status: " . $response->getStatusCode());
            error_log("=== VirtualRouteHandler::process() END (virtual route processed) ===");

            return $response;
        }

        error_log("VirtualRouteHandler: No virtual route match, passing through to next middleware");
        $response = $handler->handle($request);
        error_log("VirtualRouteHandler: Response received from next middleware, status: " . $response->getStatusCode());
        error_log("=== VirtualRouteHandler::process() END (no virtual route) ===");
        return $response;
    }

    /**
     * Get the path for the template page that PageResolver can find
     */
    private function getTemplatePagePath(array $templatePage, Site $site): string
    {
        // Use the template page slug
        $slug = $templatePage['slug'] ?? '';

        // Ensure it starts with the site base path
        $basePath = rtrim($site->getBase()->getPath(), '/');

        if (strpos($slug, $basePath) !== 0) {
            $slug = $basePath . '/' . ltrim($slug, '/');
        }

        return $slug;
    }
}
