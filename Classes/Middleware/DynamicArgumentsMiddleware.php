<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * This middleware adds dynamic arguments to the routing
 * It runs after PageArgumentValidator but before TypoScriptFrontendInitialization
 * This allows us to manipulate the dynamic arguments and cHash after routing has been processed
 */

class DynamicArgumentsMiddleware implements MiddlewareInterface
{
    /**
     * Process the request by adding dynamic arguments to the routing
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        error_log("=== DynamicArgumentsMiddleware::process() START ===");

        /** @var PageArguments|null $pageArguments */
        $pageArguments = $request->getAttribute('routing');
        error_log("DynamicArgumentsMiddleware: PageArguments type: " . (is_object($pageArguments) ? get_class($pageArguments) : gettype($pageArguments)));

        if ($pageArguments instanceof PageArguments) {
            error_log("DynamicArgumentsMiddleware: PageArguments found, page ID: " . $pageArguments->getPageId());
            // Get the current arguments
            $arguments = $pageArguments->getArguments();
            error_log("DynamicArgumentsMiddleware: Current arguments: " . json_encode($arguments));

            $virtualRouteContext = $request->getAttribute('landing-pages.virtual_route_context');
            error_log("DynamicArgumentsMiddleware: Virtual route context: " . ($virtualRouteContext ? 'FOUND' : 'NULL'));

            // Only process if we have a virtual route context
            if ($virtualRouteContext === null) {
                error_log("DynamicArgumentsMiddleware: No virtual route context, passing through");
                $response = $handler->handle($request);
                error_log("DynamicArgumentsMiddleware: Response received, status: " . $response->getStatusCode());
                error_log("=== DynamicArgumentsMiddleware::process() END (no context) ===");
                return $response;
            }

            error_log("DynamicArgumentsMiddleware: Processing virtual route context");
            $arguments['lp_route'] = $virtualRouteContext->getOriginalPath();
            $arguments['cHash'] =  sha1($virtualRouteContext->getOriginalPath());
            error_log("DynamicArgumentsMiddleware: Added arguments: " . json_encode($arguments));

            $newPageArguments = GeneralUtility::makeInstance(
                PageArguments::class,
                $pageArguments->getPageId(),
                $pageArguments->getPageType(),
                $pageArguments->getRouteArguments(),
                $pageArguments->getStaticArguments(),
                array_merge($pageArguments->getDynamicArguments(), $arguments)
            );
            error_log("DynamicArgumentsMiddleware: Created new PageArguments with page ID: " . $newPageArguments->getPageId());

            $request = $request->withAttribute('routing', $newPageArguments);
            error_log("DynamicArgumentsMiddleware: Updated request with new PageArguments, passing to next middleware");
            $response = $handler->handle($request);
            error_log("DynamicArgumentsMiddleware: Response received, status: " . $response->getStatusCode());
            error_log("=== DynamicArgumentsMiddleware::process() END (processed) ===");
            return $response;
        }

        error_log("DynamicArgumentsMiddleware: No PageArguments found, passing through");
        $response = $handler->handle($request);
        error_log("DynamicArgumentsMiddleware: Response received, status: " . $response->getStatusCode());
        error_log("=== DynamicArgumentsMiddleware::process() END (no PageArguments) ===");
        return $response;
    }
}
