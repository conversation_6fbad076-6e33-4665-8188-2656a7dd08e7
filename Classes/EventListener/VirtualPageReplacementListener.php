<?php

declare(strict_types=1);

namespace Bgs\LandingPages\EventListener;

use Bgs\LandingPages\Domain\Model\VirtualRouteContext;
use Bgs\LandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\AfterPageWithRootLineIsResolvedEvent;

/**
 * Virtual Page Replacement Listener
 * 
 * Listens to AfterPageWithRootLineIsResolvedEvent to replace the resolved page
 * with virtual page data when processing virtual routes.
 */
class VirtualPageReplacementListener
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    public function __invoke(AfterPageWithRootLineIsResolvedEvent $event): void
    {
        error_log("=== VirtualPageReplacementListener::__invoke() START ===");

        // Get virtual route data directly from the request attribute (set by middleware)
        $request = $event->getRequest();

        // Check both possible attribute names for debugging
        $virtualRouteContext1 = $request->getAttribute('flight_landing_pages.virtual_route_context');
        $virtualRouteContext2 = $request->getAttribute('landing-pages.virtual_route_context');

        error_log("VirtualPageReplacementListener: flight_landing_pages.virtual_route_context = " . ($virtualRouteContext1 ? 'FOUND' : 'NULL'));
        error_log("VirtualPageReplacementListener: landing-pages.virtual_route_context = " . ($virtualRouteContext2 ? 'FOUND' : 'NULL'));

        $virtualRouteContext = $virtualRouteContext1 ?: $virtualRouteContext2;

        if (!$virtualRouteContext instanceof VirtualRouteContext || !$virtualRouteContext->isVirtualRoute()) {
            error_log("VirtualPageReplacementListener: No virtual route context found or not a virtual route, returning");
            error_log("=== VirtualPageReplacementListener::__invoke() END (no virtual route) ===");
            return;
        }

        // Get the controller and access page data directly
        $controller = $event->getController();
        $currentPage = $controller->page; // This is now the template page (due to setPageId)
        $currentRootLine = $controller->rootLine;

        // Get landing page data from virtual route context
        $landingPageData = $virtualRouteContext->getLandingPageData();
        $routeData = $virtualRouteContext->getFlightRouteData();

        // Create a hybrid page: landing page structure + template page content
        // Start with template page (for TypoScript resolution) but use landing page properties
        $modifiedPage = $currentPage; // Start with template page

        // Replace structural properties with landing page data
        $structuralFields = [
            'uid',      // Use landing page UID for routing
            'pid',      // Use landing page PID for hierarchy
            'slug',     // Use landing page slug for routing
            'sorting',  // Use landing page sorting
            'crdate',   // Use landing page creation date
            // Keep other structural fields from landing page
            'deleted',
            'hidden',
            'starttime',
            'endtime',
            'fe_group',
            'sys_language_uid',
            'l10n_parent',
            'l10n_source',
        ];

        // Update the rootline to include our modified page
        $modifiedRootLine = $currentRootLine;
        if (!empty($modifiedRootLine)) {
            // Replace the last entry (current page) with our modified page
            $modifiedRootLine[array_key_first($modifiedRootLine)] = $modifiedPage;
        }

        // Set the modified page and rootline directly on the controller
        error_log("VirtualPageReplacementListener: Setting modified page and rootline on controller");
        $controller->page = $modifiedPage;
        $controller->rootLine = $modifiedRootLine;
        error_log("=== VirtualPageReplacementListener::__invoke() END (virtual route processed) ===");
    }
}
