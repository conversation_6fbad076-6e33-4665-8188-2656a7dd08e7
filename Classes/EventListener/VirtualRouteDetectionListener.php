<?php

declare(strict_types=1);

namespace Bgs\LandingPages\EventListener;

use Bgs\LandingPages\Domain\Model\VirtualRouteContext;
use Bgs\LandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent;

/**
 * Virtual Route Detection Listener
 * 
 * Listens to BeforePageIsResolvedEvent to detect virtual routes
 * and store the context for later processing.
 */
class VirtualRouteDetectionListener
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    public function __invoke(BeforePageIsResolvedEvent $event): void
    {
        error_log("=== VirtualRouteDetectionListener::__invoke() START ===");

        $request = $event->getRequest();

        // Check if this is a virtual route request (set by our middleware)
        $virtualRouteContext = $request->getAttribute('landing-pages.virtual_route_context');
        error_log("VirtualRouteDetectionListener: Virtual route context = " . ($virtualRouteContext ? 'FOUND' : 'NULL'));

        if ($virtualRouteContext instanceof VirtualRouteContext && $virtualRouteContext->isVirtualRoute()) {
            error_log("VirtualRouteDetectionListener: Processing virtual route");
            // Store virtual route context for later processing by other event listeners
            // Convert to legacy format for backward compatibility with existing service
            $this->virtualRouteService->setVirtualRoute($virtualRouteContext->toVirtualRouteMatch());
        } else {
            error_log("VirtualRouteDetectionListener: Clearing virtual route state for normal request");
            // Clear any previous virtual route state for normal requests
            $this->virtualRouteService->clearVirtualRoute();
        }
        error_log("=== VirtualRouteDetectionListener::__invoke() END ===");
    }
}
